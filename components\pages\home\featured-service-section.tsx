import { ServiceCard } from "@/components/service-card";
import { But<PERSON> } from "@/components/ui/button";
import { featuredServices } from "@/lib/data";
import Link from "next/link";

export default function FeaturedServiceSection() {
   return (
      <>
         <section className="py-20">
            <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Our Services
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     From intimate moments to grand celebrations, we offer
                     comprehensive photography and videography services tailored
                     to your unique needs.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                  {featuredServices.map((service, index) => (
                     <ServiceCard key={index} {...service} />
                  ))}
               </div>

               <div className="text-center">
                  <Button asChild size="lg">
                     <Link href="/services">View All Services</Link>
                  </Button>
               </div>
            </div>
         </section>
      </>
   );
}
