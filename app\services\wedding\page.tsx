import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ImageGallery } from "@/components/image-gallery";
import Link from "next/link";
import { Heart, Camera, Clock, Users, Star, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Essential",
    price: "£800",
    duration: "6 hours",
    features: [
      "6 hours of wedding day coverage",
      "Professional photographer",
      "200+ edited high-resolution photos",
      "Online gallery for 1 year",
      "Print release included",
      "USB drive with all photos"
    ]
  },
  {
    name: "Premium",
    price: "£1,200",
    duration: "8 hours",
    features: [
      "8 hours of wedding day coverage",
      "Lead photographer + assistant",
      "400+ edited high-resolution photos",
      "Engagement session included",
      "Online gallery for 2 years",
      "Print release included",
      "USB drive + 20 premium prints",
      "Same day preview (10 photos)"
    ],
    popular: true
  },
  {
    name: "Luxury",
    price: "£1,800",
    duration: "Full day",
    features: [
      "Full day coverage (12+ hours)",
      "Lead photographer + 2 assistants",
      "600+ edited high-resolution photos",
      "Engagement session included",
      "Second shooter for ceremony",
      "Online gallery for 3 years",
      "Print release included",
      "Premium photo album (50 pages)",
      "USB drive + 50 premium prints",
      "Same day preview (20 photos)"
    ]
  }
];

const galleryImages = [
  { src: "/images/wedding-shoots/wedding-shoot-1.JPG", alt: "Wedding ceremony moment" },
  { src: "/images/wedding-shoots/wedding-shoot-2.PNG", alt: "Bride and groom portrait" },
  { src: "/images/wedding-shoots/wedding-shoot-3.JPG", alt: "Wedding reception" },
  { src: "/images/wedding-shoots/wedding-shoot-4.JPG", alt: "Wedding details" },
  { src: "/images/wedding-shoots/wedding-shoot-5.JPG", alt: "Wedding party" },
  { src: "/images/wedding-shoots/wedding-shoot-6.JPG", alt: "Wedding venue" }
];

const features = [
  {
    icon: Heart,
    title: "Romantic Storytelling",
    description: "We capture the love story of your special day with artistic vision and emotional depth."
  },
  {
    icon: Camera,
    title: "Professional Equipment",
    description: "State-of-the-art cameras and lenses ensure stunning image quality in any lighting condition."
  },
  {
    icon: Clock,
    title: "Flexible Coverage",
    description: "From intimate ceremonies to grand celebrations, we adapt our coverage to your timeline."
  },
  {
    icon: Users,
    title: "Experienced Team",
    description: "Our skilled photographers have captured hundreds of weddings with professionalism and care."
  }
];

export default function WeddingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/wedding-shoots/wedding-shoot-1.JPG"
            alt="Wedding photography hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Wedding Photography
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Capture the magic of your special day with timeless photographs that tell your unique love story
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Wedding</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">View Wedding Gallery</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Us for Your Wedding</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We understand that your wedding day is one of the most important days of your life. Here's what makes our wedding photography special.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <feature.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Wedding Photography Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your special day. All packages include professional editing and online gallery access.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative ${pkg.popular ? 'ring-2 ring-primary' : ''}`}>
                {pkg.popular && (
                  <Badge className="absolute -top-3 left-1/2 -translate-x-1/2">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} coverage</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Choose This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Recent Wedding Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Take a look at some of our recent wedding photography work that showcases our style and attention to detail.
            </p>
          </div>
          
          <ImageGallery images={galleryImages} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Book Your Wedding Photography?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let's discuss your wedding day and create a photography package that perfectly captures your love story.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Get Your Wedding Quote</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
