import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageGallery } from "@/components/image-gallery";
import Link from "next/link";
import { Heart, Camera, Home, Users, CheckCircle, Baby } from "lucide-react";

const packages = [
  {
    name: "Essential",
    price: "£250",
    duration: "1 hour",
    features: [
      "1 hour maternity session",
      "Studio or outdoor location",
      "30+ edited high-resolution photos",
      "Online gallery for 1 year",
      "Print release included",
      "Wardrobe consultation"
    ]
  },
  {
    name: "Premium",
    price: "£350",
    duration: "1.5 hours",
    features: [
      "1.5 hour maternity session",
      "Studio and outdoor options",
      "50+ edited high-resolution photos",
      "Partner and family included",
      "Online gallery for 2 years",
      "Print release included",
      "Wardrobe consultation",
      "Same day preview (5 photos)"
    ]
  },
  {
    name: "Luxury",
    price: "£500",
    duration: "2 hours",
    features: [
      "2 hour maternity session",
      "Multiple locations/setups",
      "80+ edited high-resolution photos",
      "Partner and family included",
      "Professional styling advice",
      "Online gallery for 3 years",
      "Print release included",
      "Same day preview (10 photos)",
      "USB drive + 15 premium prints"
    ]
  }
];

const galleryImages = [
  { src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg", alt: "Maternity photography session" },
  { src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG", alt: "Pregnancy portrait outdoors" },
  { src: "/images/pregnancy-shoots/pregnancy-shoot-3.jpg", alt: "Expecting mother photography" },
  { src: "/images/pregnancy-shoots/pregnancy-shoot-4.jpg", alt: "Maternity couple session" },
  { src: "/images/pregnancy-shoots/pregnancy-shoot-5.jpg", alt: "Pregnancy photography studio" },
  { src: "/images/pregnancy-shoots/pregnancy-shoot-6.jpg", alt: "Maternity lifestyle photography" }
];

const features = [
  {
    icon: Heart,
    title: "Celebrate This Journey",
    description: "Capture the beauty and emotion of pregnancy with elegant, artistic portraits that celebrate this special time."
  },
  {
    icon: Camera,
    title: "Professional Expertise",
    description: "Our experienced photographers specialize in maternity photography, ensuring comfortable and beautiful sessions."
  },
  {
    icon: Home,
    title: "Flexible Locations",
    description: "Choose from our professional studio, outdoor locations, or the comfort of your own home."
  },
  {
    icon: Users,
    title: "Include Your Family",
    description: "Include your partner and children in the session to capture the growing family dynamic."
  }
];

const tips = [
  {
    title: "Best Timing",
    description: "The ideal time for maternity photos is between 28-36 weeks when your bump is beautifully rounded but you're still comfortable."
  },
  {
    title: "What to Wear",
    description: "We provide wardrobe consultation and have a selection of maternity gowns available for your session."
  },
  {
    title: "Comfort First",
    description: "We ensure you're comfortable throughout the session with breaks, seating, and a relaxed pace."
  },
  {
    title: "Include Partners",
    description: "Partners and children are welcome to join for some shots to capture the excitement of the growing family."
  }
];

export default function PregnancyPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/pregnancy-shoots/pregnancy-shoot-1.jpg"
            alt="Pregnancy photography hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Pregnancy Photography
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Celebrate this magical time with elegant maternity portraits that capture the beauty and anticipation of pregnancy
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Session</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">View Gallery</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Our Maternity Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We specialize in creating beautiful, comfortable maternity photography sessions that celebrate this incredible journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <feature.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tips Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Maternity Photography Tips</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Here are some helpful tips to ensure you get the most out of your maternity photography session.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {tips.map((tip, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Baby className="h-5 w-5 text-primary mr-2" />
                    {tip.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    {tip.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Maternity Photography Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your maternity session. All packages include professional editing and online gallery access.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} session</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Choose This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Recent Maternity Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Browse through our recent maternity photography sessions to see our style and approach to capturing this special time.
            </p>
          </div>
          
          <ImageGallery images={galleryImages} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Capture This Special Time?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let's create beautiful maternity photos that celebrate your pregnancy journey and the anticipation of your new arrival.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Book Your Maternity Session</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
