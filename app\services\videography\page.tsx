import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Video, Camera, Music, Edit, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Essential",
    price: "£600",
    duration: "6 hours",
    features: [
      "6 hours of event coverage",
      "Professional 4K recording",
      "Highlight reel (3-5 minutes)",
      "Raw footage provided",
      "Basic color correction",
      "Digital delivery"
    ]
  },
  {
    name: "Premium",
    price: "£900",
    duration: "8 hours",
    features: [
      "8 hours of event coverage",
      "Professional 4K recording",
      "Highlight reel (5-8 minutes)",
      "Ceremony edit (full length)",
      "Drone footage (if permitted)",
      "Professional audio recording",
      "Color grading & editing",
      "Digital delivery + USB"
    ]
  },
  {
    name: "Luxury",
    price: "£1,400",
    duration: "Full day",
    features: [
      "Full day coverage (12+ hours)",
      "Multi-camera setup",
      "Cinematic highlight reel (8-12 minutes)",
      "Full ceremony & reception edits",
      "Drone footage (if permitted)",
      "Professional audio & music",
      "Advanced color grading",
      "Same day highlights",
      "Digital delivery + premium USB"
    ]
  }
];

const features = [
  {
    icon: Video,
    title: "4K Quality",
    description: "Professional 4K recording ensures crystal clear video quality that looks stunning on any screen."
  },
  {
    icon: Camera,
    title: "Multi-Camera Setup",
    description: "Multiple camera angles capture every moment and emotion from different perspectives."
  },
  {
    icon: Music,
    title: "Professional Audio",
    description: "High-quality audio recording and music selection create an immersive viewing experience."
  },
  {
    icon: Edit,
    title: "Expert Editing",
    description: "Professional editing and color grading bring your story to life with cinematic quality."
  }
];

export default function VideographyPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/videography.jpg"
            alt="Videography services hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Professional Videography
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Capture your special moments in motion with cinematic quality videography that tells your story beautifully
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Videography</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">View Our Work</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Our Videography Services</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We combine technical expertise with creative storytelling to produce videos that capture the emotion and beauty of your special day.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <feature.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Videography Services</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We offer comprehensive videography services for various events and occasions.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Wedding Videography</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Complete wedding day coverage including ceremony, reception, and highlight reels that tell your love story.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Event Documentation</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Professional documentation of corporate events, celebrations, and special occasions with full editing services.
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Promotional Videos</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Business promotional videos, testimonials, and marketing content that showcases your brand professionally.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Videography Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect videography package for your event. All packages include professional editing and digital delivery.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} coverage</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Choose This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Create Your Video Story?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let's discuss your videography needs and create a cinematic experience that captures your special moments perfectly.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Get Your Videography Quote</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
