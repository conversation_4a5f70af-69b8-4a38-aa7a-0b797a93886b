import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageGallery } from "@/components/image-gallery";
import Link from "next/link";
import { Heart, Camera, Users, Clock, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Essential",
    price: "£200",
    duration: "2 hours",
    features: [
      "2 hours ceremony coverage",
      "Ceremony and family portraits",
      "50+ edited high-resolution photos",
      "Online gallery for 1 year",
      "Print release included"
    ]
  },
  {
    name: "Premium",
    price: "£300",
    duration: "3 hours",
    features: [
      "3 hours extended coverage",
      "Ceremony, portraits & reception",
      "100+ edited high-resolution photos",
      "Candid moments captured",
      "Online gallery for 2 years",
      "Print release included",
      "Same day preview (10 photos)"
    ]
  }
];

const galleryImages = [
  { src: "/images/child-dedication/child-dedication-1.JPG", alt: "Child dedication ceremony" },
  { src: "/images/child-dedication/child-dedication-2.JPG", alt: "Family during dedication" },
  { src: "/images/child-dedication/child-dedication-3.JPG", alt: "Child dedication portraits" },
  { src: "/images/child-dedication/child-dedication-4.JPG", alt: "Dedication ceremony moments" },
  { src: "/images/child-dedication/child-dedication-5.JPG", alt: "Family celebration" },
  { src: "/images/child-dedication/child-dedication-6.JPG", alt: "Child dedication photography" }
];

export default function ChildDedicationPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/child-dedication/child-dedication-1.JPG"
            alt="Child dedication photography hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Child Dedication Photography
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Document these precious ceremonies with sensitivity and artistic vision, capturing the joy and significance of this special milestone
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Session</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">View Gallery</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">Capturing Sacred Moments</h2>
            <div className="prose prose-lg mx-auto text-muted-foreground">
              <p className="mb-6">
                Child dedication ceremonies are deeply meaningful events that celebrate the commitment of parents and community 
                to nurture and guide a child's spiritual journey. We understand the sacred nature of these ceremonies and 
                approach each event with respect, sensitivity, and artistic excellence.
              </p>
              <p className="mb-6">
                Our photography captures not just the formal ceremony, but also the intimate family moments, the joy of 
                grandparents, and the love that surrounds your child on this special day. We work discreetly to document 
                these precious moments without disrupting the solemnity of the occasion.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Approach</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We combine respectful documentation with artistic photography to create lasting memories of this important milestone.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <Heart className="h-12 w-12 text-primary" />
                </div>
                <CardTitle>Respectful Documentation</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  We work quietly and respectfully, capturing moments without disrupting the ceremony's sacred atmosphere.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <Camera className="h-12 w-12 text-primary" />
                </div>
                <CardTitle>Professional Quality</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  High-quality photography that captures both the formal ceremony and candid family moments.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <Users className="h-12 w-12 text-primary" />
                </div>
                <CardTitle>Family Focus</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  We ensure all family members are included, from grandparents to siblings, capturing the complete family story.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <Clock className="h-12 w-12 text-primary" />
                </div>
                <CardTitle>Flexible Coverage</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center">
                  Coverage options from ceremony-only to extended family celebration photography.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Photography Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your child dedication ceremony. All packages include professional editing and online gallery access.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} coverage</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Choose This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Recent Child Dedication Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Browse through our recent child dedication photography to see how we capture these meaningful ceremonies.
            </p>
          </div>
          
          <ImageGallery images={galleryImages} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Document This Special Day?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let us help you preserve the memories of your child's dedication ceremony with beautiful, respectful photography.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Book Your Child Dedication Photography</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
