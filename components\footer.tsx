import { Instagram, Mail, MessageCircle, Phone } from "lucide-react";
import Link from "next/link";

const services = [
   { name: "Wedding Photography", href: "/services/wedding" },
   { name: "Pre-Wedding Shoots", href: "/services/pre-wedding" },
   { name: "Pregnancy Photography", href: "/services/pregnancy" },
   { name: "Child Dedication", href: "/services/child-dedication" },
   { name: "Videography", href: "/services/videography" },
   { name: "360 Video Booth", href: "/services/360-booth" },
];

const quickLinks = [
   { name: "Home", href: "/" },
   { name: "About", href: "/about" },
   { name: "Services", href: "/services" },
   { name: "Portfolio", href: "/portfolio" },
   { name: "Contact", href: "/contact" },
];

export function Footer() {
   return (
      <footer className="bg-astral-grey border-t border-astral-grey-light">
         <div className="container max-w-[1400px] mx-auto px-4 py-12">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
               {/* Brand */}
               <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                     <span className="text-xl font-playfair font-bold bg-gradient-accent bg-clip-text text-transparent">
                        Astral Studios
                     </span>
                  </div>
                  <p className="text-muted-foreground font-montserrat">
                     Professional photography and videography services capturing
                     your most precious moments.
                  </p>
                  <p className="text-sm text-muted-foreground font-montserrat">
                     astralstudios.co.uk
                  </p>
               </div>

               {/* Quick Links */}
               <div className="space-y-4">
                  <h3 className="text-lg font-playfair font-semibold text-foreground">
                     Quick Links
                  </h3>
                  <div className="flex flex-col space-y-2">
                     {quickLinks.map((link) => (
                        <Link
                           key={link.name}
                           href={link.href}
                           className="text-muted-foreground hover:text-primary transition-colors w-fit"
                        >
                           {link.name}
                        </Link>
                     ))}
                  </div>
               </div>

               {/* Services */}
               <div className="space-y-4">
                  <h3 className="text-lg font-playfair font-semibold text-foreground">
                     Services
                  </h3>
                  <div className="flex flex-col space-y-2">
                     {services.map((service) => (
                        <Link
                           key={service.name}
                           href={service.href}
                           className="text-muted-foreground hover:text-primary transition-colors w-fit"
                        >
                           {service.name}
                        </Link>
                     ))}
                  </div>
               </div>

               {/* Contact */}
               <div className="space-y-4">
                  <h3 className="text-lg font-playfair font-semibold text-foreground">
                     Contact
                  </h3>
                  <div className="flex flex-col space-y-3">
                     <Link
                        href="mailto:<EMAIL>"
                        className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors font-montserrat"
                     >
                        <Mail className="h-4 w-4" />
                        <span><EMAIL></span>
                     </Link>
                     <Link
                        href="tel:+447886161245"
                        className="flex items-center space-x-2 text-muted-foreground hover:text-primary transition-colors font-montserrat"
                     >
                        <Phone className="h-4 w-4" />
                        <span>+44 7886 161245</span>
                     </Link>
                  </div>

                  {/* Social Media */}
                  <div className="flex space-x-4 pt-2">
                     <a
                        href="https://instagram.com/astralstudios"
                        className="p-2 bg-astral-grey-light rounded-lg hover:bg-primary transition-colors"
                     >
                        <Instagram className="h-5 w-5" />
                     </a>
                     <a
                        href="https://tiktok.com/@astralstudioz"
                        className="p-2 bg-astral-grey-light rounded-lg hover:bg-primary transition-colors"
                     >
                        <MessageCircle className="h-5 w-5" />
                     </a>
                  </div>
               </div>
            </div>

            <div className="border-t border-astral-grey-light mt-8 pt-8 text-center">
               <p className="text-muted-foreground font-montserrat">
                  © 2024 Astral Studios. All Rights Reserved.
               </p>
            </div>
         </div>
      </footer>
   );
}
