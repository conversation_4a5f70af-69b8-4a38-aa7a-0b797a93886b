import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageGallery } from "@/components/image-gallery";
import Link from "next/link";
import { MapPin, Camera, Clock, Heart, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Classic",
    price: "£300",
    duration: "2 hours",
    features: [
      "2 hours photo session",
      "1 location of your choice",
      "50+ edited high-resolution photos",
      "Online gallery for 1 year",
      "Print release included",
      "Same day preview (5 photos)"
    ]
  },
  {
    name: "Premium",
    price: "£450",
    duration: "3 hours",
    features: [
      "3 hours photo session",
      "2 locations of your choice",
      "100+ edited high-resolution photos",
      "Outfit change assistance",
      "Online gallery for 2 years",
      "Print release included",
      "Same day preview (10 photos)",
      "USB drive with all photos"
    ]
  },
  {
    name: "Luxury",
    price: "£650",
    duration: "4 hours",
    features: [
      "4 hours photo session",
      "3 locations of your choice",
      "150+ edited high-resolution photos",
      "Multiple outfit changes",
      "Professional styling advice",
      "Online gallery for 3 years",
      "Print release included",
      "Same day preview (15 photos)",
      "USB drive + 20 premium prints"
    ]
  }
];

const galleryImages = [
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-1.JPG", alt: "Pre-wedding couple portrait" },
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-2.JPG", alt: "Engagement session outdoors" },
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-3.JPG", alt: "Romantic couple photography" },
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-4.JPG", alt: "Pre-wedding lifestyle shoot" },
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-5.JPG", alt: "Couple in natural setting" },
  { src: "/images/pre-wedding-shoots/pre-wedding-shoots-6.JPG", alt: "Engagement photography session" }
];

const locations = [
  {
    name: "Urban Cityscapes",
    description: "Modern architecture, street art, and city lights create a contemporary backdrop for your love story."
  },
  {
    name: "Natural Landscapes",
    description: "Beautiful parks, gardens, and countryside locations provide a romantic and timeless setting."
  },
  {
    name: "Historic Venues",
    description: "Elegant buildings, bridges, and landmarks add sophistication and character to your photos."
  },
  {
    name: "Beach & Waterfront",
    description: "Coastal locations offer stunning natural light and romantic sunset opportunities."
  }
];

const benefits = [
  {
    icon: Camera,
    title: "Get Comfortable",
    description: "Pre-wedding shoots help you get comfortable with your photographer before the big day."
  },
  {
    icon: Heart,
    title: "Capture Your Love",
    description: "Document your relationship and love story in a relaxed, intimate setting."
  },
  {
    icon: MapPin,
    title: "Meaningful Locations",
    description: "Choose locations that are special to your relationship and tell your unique story."
  },
  {
    icon: Clock,
    title: "No Time Pressure",
    description: "Unlike wedding day, we have plenty of time to create the perfect shots without rushing."
  }
];

export default function PreWeddingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/pre-wedding-shoots/pre-wedding-shoots-1.JPG"
            alt="Pre-wedding photography hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Pre-Wedding Shoots
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Beautiful engagement and pre-wedding photography sessions that capture your love story in stunning locations
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Session</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">View Gallery</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Pre-Wedding Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Pre-wedding shoots offer the perfect opportunity to capture your love story in a relaxed setting while preparing for your wedding day.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <benefit.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Locations Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Popular Shoot Locations</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We can shoot at any location that's meaningful to you, or choose from these popular options that provide stunning backdrops.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {locations.map((location, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 text-primary mr-2" />
                    {location.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    {location.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Pre-Wedding Photography Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your pre-wedding session. All packages include professional editing and online gallery access.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} session</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Choose This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Recent Pre-Wedding Photography</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Browse through our recent pre-wedding and engagement photography sessions to see our style and approach.
            </p>
          </div>
          
          <ImageGallery images={galleryImages} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Capture Your Love Story?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let's create beautiful pre-wedding photos that celebrate your relationship and prepare you for your wedding day.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Book Your Pre-Wedding Session</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
