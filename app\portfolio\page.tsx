"use client";

import { useState } from "react";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

const categories = [
  { id: "all", name: "All Work", count: 45 },
  { id: "wedding", name: "Weddings", count: 15 },
  { id: "pre-wedding", name: "Pre-Wedding", count: 9 },
  { id: "pregnancy", name: "Pregnancy", count: 9 },
  { id: "child-dedication", name: "Child Dedication", count: 9 },
  { id: "videography", name: "Videography", count: 1 },
  { id: "events", name: "Events", count: 2 }
];

const portfolioImages = {
  wedding: [
    { src: "/images/wedding-shoots/wedding-shoot-1.JPG", alt: "Wedding ceremony moment", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-2.PNG", alt: "Bride and groom portrait", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-3.JPG", alt: "Wedding reception", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-4.JPG", alt: "Wedding details", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-5.JPG", alt: "Wedding party", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-6.JPG", alt: "Wedding venue", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-7.JPG", alt: "Wedding couple", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-8.JPG", alt: "Wedding celebration", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-9.JPG", alt: "Wedding photography", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-10.JPG", alt: "Wedding moments", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-11.JPG", alt: "Wedding day", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-12.JPG", alt: "Wedding ceremony", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-13.jpg", alt: "Wedding portrait", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-14.PNG", alt: "Wedding reception", category: "wedding" },
    { src: "/images/wedding-shoots/wedding-shoot-15.PNG", alt: "Wedding celebration", category: "wedding" }
  ],
  "pre-wedding": [
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-1.JPG", alt: "Pre-wedding couple portrait", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-2.JPG", alt: "Engagement session outdoors", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-3.JPG", alt: "Romantic couple photography", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-4.JPG", alt: "Pre-wedding lifestyle shoot", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-5.JPG", alt: "Couple in natural setting", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-6.JPG", alt: "Engagement photography session", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-7.JPG", alt: "Pre-wedding portraits", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-8.JPG", alt: "Couple photography", category: "pre-wedding" },
    { src: "/images/pre-wedding-shoots/pre-wedding-shoots-9.JPG", alt: "Engagement shoot", category: "pre-wedding" }
  ],
  pregnancy: [
    { src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg", alt: "Maternity photography session", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG", alt: "Pregnancy portrait outdoors", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-3.jpg", alt: "Expecting mother photography", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-4.jpg", alt: "Maternity couple session", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-5.jpg", alt: "Pregnancy photography studio", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-6.jpg", alt: "Maternity lifestyle photography", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-7.JPG", alt: "Pregnancy portraits", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-8.JPG", alt: "Maternity photography", category: "pregnancy" },
    { src: "/images/pregnancy-shoots/pregnancy-shoot-9.JPG", alt: "Pregnancy session", category: "pregnancy" }
  ],
  "child-dedication": [
    { src: "/images/child-dedication/child-dedication-1.JPG", alt: "Child dedication ceremony", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-2.JPG", alt: "Family during dedication", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-3.JPG", alt: "Child dedication portraits", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-4.JPG", alt: "Dedication ceremony moments", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-5.JPG", alt: "Family celebration", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-6.JPG", alt: "Child dedication photography", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-7.PNG", alt: "Dedication ceremony", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-8.JPG", alt: "Family moments", category: "child-dedication" },
    { src: "/images/child-dedication/child-dedication-9.JPG", alt: "Child dedication event", category: "child-dedication" }
  ],
  videography: [
    { src: "/images/videography.jpg", alt: "Professional videography services", category: "videography" }
  ],
  events: [
    { src: "/images/360-photo-booth.jpg", alt: "360 video booth at event", category: "events" },
    { src: "/images/dry-ice-machine.jpg", alt: "Dry ice effects at event", category: "events" }
  ]
};

const getAllImages = () => {
  return Object.values(portfolioImages).flat();
};

export default function PortfolioPage() {
  const [activeCategory, setActiveCategory] = useState("all");

  const getFilteredImages = () => {
    if (activeCategory === "all") {
      return getAllImages();
    }
    return portfolioImages[activeCategory as keyof typeof portfolioImages] || [];
  };

  const filteredImages = getFilteredImages();

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-background to-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Our <span className="text-primary">Portfolio</span>
          </h1>
          <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            Explore our collection of beautiful photography and videography work. Each image tells a story, 
            captures an emotion, and preserves a precious moment in time.
          </p>
          <Button asChild size="lg">
            <Link href="/contact">Start Your Project</Link>
          </Button>
        </div>
      </section>

      {/* Filter Categories */}
      <section className="py-12 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={activeCategory === category.id ? "default" : "outline"}
                onClick={() => setActiveCategory(category.id)}
                className="flex items-center gap-2"
              >
                {category.name}
                <Badge variant="secondary" className="text-xs">
                  {category.count}
                </Badge>
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold mb-4">
              {categories.find(cat => cat.id === activeCategory)?.name || "All Work"}
            </h2>
            <p className="text-muted-foreground">
              {filteredImages.length} {filteredImages.length === 1 ? 'image' : 'images'} in this category
            </p>
          </div>
          
          <ImageGallery images={filteredImages} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Love What You See?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Let's create something beautiful together. Contact us to discuss your photography and videography needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary">
              <Link href="/contact">Get Your Quote</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
              <Link href="/services">View Our Services</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
