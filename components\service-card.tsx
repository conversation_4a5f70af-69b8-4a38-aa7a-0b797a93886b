import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";

interface ServiceCardProps {
   title: string;
   description: string;
   image: string;
   href: string;
   features?: string[];
   className?: string;
}

export function ServiceCard({
   title,
   description,
   image,
   href,
   features = [],
   className = "",
}: ServiceCardProps) {
   return (
      <Card
         className={`overflow-hidden group hover:shadow-lg transition-shadow pt-0 duration-300 ${className}`}
      >
         <div className="relative aspect-[4/3] overflow-hidden">
            <Image
               src={image}
               alt={title}
               fill
               className="object-cover transition-transform duration-300 group-hover:scale-105"
               sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
         </div>

         <CardHeader>
            <CardTitle className="text-xl text-muted">{title}</CardTitle>
            <CardDescription className="text-muted-foreground">
               {description}
            </CardDescription>
         </CardHeader>

         {features.length > 0 && (
            <CardContent className="pt-0">
               <ul className="space-y-1 text-sm text-muted-foreground mb-4">
                  {features.map((feature, index) => (
                     <li key={index} className="flex items-center">
                        <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0" />
                        {feature}
                     </li>
                  ))}
               </ul>
            </CardContent>
         )}

         <CardContent className="pt-0">
            <Button asChild className="w-full">
               <Link href={href}>Learn More</Link>
            </Button>
         </CardContent>
      </Card>
   );
}
