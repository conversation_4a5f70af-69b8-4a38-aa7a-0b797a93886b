import { Footer } from "@/components/footer";
import { Navigation } from "@/components/navigation";
import { StructuredData } from "@/components/structured-data";
import type { Metadata } from "next";
import { Mont<PERSON>rat } from "next/font/google";
import "./globals.css";

const montserrat = Montserrat({
   variable: "--font-montserrat",
   subsets: ["latin"],
   display: "swap",
});

export const metadata: Metadata = {
   title: "Astral Studios - Professional Photography & Videography Services",
   description:
      "Professional photography and videography services in the UK. Specializing in weddings, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth rental, and dry ice machine rental.",
   keywords:
      "photography, videography, wedding photography, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth, dry ice machine rental, UK photography studio",
   authors: [{ name: "Astral Studios" }],
   creator: "Astral Studios",
   publisher: "Astral Studios",
   metadataBase: new URL("http://astralstudios.co.uk"),
   openGraph: {
      title: "Astral Studios - Professional Photography & Videography",
      description:
         "Professional photography and videography services specializing in weddings, pre-wedding shoots, pregnancy photography, and more.",
      url: "http://astralstudios.co.uk",
      siteName: "Astral Studios",
      type: "website",
      images: [
         {
            url: "/images/hero.JPG",
            width: 1200,
            height: 630,
            alt: "Astral Studios - Professional Photography",
         },
      ],
   },
   twitter: {
      card: "summary_large_image",
      title: "Astral Studios - Professional Photography & Videography",
      description:
         "Professional photography and videography services in the UK",
      images: ["/images/hero.JPG"],
   },
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en">
         <body className={`${montserrat.className} antialiased`}>
            <StructuredData type="organization" />
            <StructuredData type="localBusiness" />
            <Navigation />
            <main className="min-h-screen">{children}</main>
            <Footer />
         </body>
      </html>
   );
}
