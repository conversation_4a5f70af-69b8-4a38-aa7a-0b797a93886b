import { ContactForm } from "@/components/contact-form";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mail, Phone, MapPin, Clock, Instagram, MessageCircle } from "lucide-react";
import Link from "next/link";

const contactInfo = [
  {
    icon: Mail,
    title: "Email",
    details: "<EMAIL>",
    href: "mailto:<EMAIL>"
  },
  {
    icon: Phone,
    title: "Phone",
    details: "+44 7886 161 245",
    href: "tel:+447886161245"
  },
  {
    icon: MapPin,
    title: "Location",
    details: "United Kingdom",
    href: "#"
  },
  {
    icon: Clock,
    title: "Response Time",
    details: "Within 24 hours",
    href: "#"
  }
];

const socialMedia = [
  {
    name: "Instagram",
    icon: Instagram,
    handle: "@astralstudios",
    href: "https://instagram.com/astralstudios",
    description: "Follow us for daily inspiration and behind-the-scenes content"
  },
  {
    name: "WhatsApp",
    icon: MessageCircle,
    handle: "07865 000 828",
    href: "https://wa.me/447865000828",
    description: "Quick questions? Message us directly on WhatsApp"
  },
  {
    name: "TikTok",
    icon: () => (
      <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
      </svg>
    ),
    handle: "@astralstudioz",
    href: "https://tiktok.com/@astralstudioz",
    description: "Check out our creative video content and photography tips"
  }
];

const faqs = [
  {
    question: "How far in advance should I book?",
    answer: "We recommend booking 3-6 months in advance for weddings and 2-4 weeks for other sessions to ensure availability."
  },
  {
    question: "Do you travel for shoots?",
    answer: "Yes, we travel throughout the UK for shoots. Travel costs may apply for locations outside our local area."
  },
  {
    question: "How long until I receive my photos?",
    answer: "Wedding photos are typically delivered within 4-6 weeks, while other sessions are ready within 1-2 weeks."
  },
  {
    question: "Can I request specific shots?",
    answer: "Absolutely! We encourage you to share your vision and any specific shots you'd like. We'll work together to create your perfect gallery."
  },
  {
    question: "What happens if it rains?",
    answer: "We always have backup plans for outdoor shoots. We can reschedule or move to covered locations while still capturing beautiful photos."
  },
  {
    question: "Do you offer payment plans?",
    answer: "Yes, we offer flexible payment plans to make our services accessible. Contact us to discuss options that work for your budget."
  }
];

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-background to-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Get In <span className="text-primary">Touch</span>
          </h1>
          <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            Ready to capture your special moments? We'd love to hear about your vision and discuss how we can 
            bring your photography and videography dreams to life.
          </p>
        </div>
      </section>

      {/* Contact Form and Info */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <ContactForm />
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-bold mb-6">Contact Information</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {contactInfo.map((info, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <info.icon className="h-5 w-5 text-primary" />
                          <CardTitle className="text-lg">{info.title}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        {info.href !== "#" ? (
                          <a 
                            href={info.href} 
                            className="text-muted-foreground hover:text-primary transition-colors"
                          >
                            {info.details}
                          </a>
                        ) : (
                          <span className="text-muted-foreground">{info.details}</span>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Social Media */}
              <div>
                <h3 className="text-xl font-bold mb-4">Follow Us</h3>
                <div className="space-y-4">
                  {socialMedia.map((social, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <social.icon className="h-6 w-6 text-primary" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-semibold">{social.name}</span>
                              <a 
                                href={social.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-primary hover:underline"
                              >
                                {social.handle}
                              </a>
                            </div>
                            <p className="text-sm text-muted-foreground">{social.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Business Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 text-primary mr-2" />
                    Business Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Monday - Friday</span>
                      <span className="text-muted-foreground">9:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Saturday</span>
                      <span className="text-muted-foreground">10:00 AM - 4:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sunday</span>
                      <span className="text-muted-foreground">By Appointment</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-4">
                    * Weekend shoots and events are available by arrangement
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Here are answers to some common questions about our photography and videography services.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {faq.answer}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Start Your Project?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Don't wait to capture your precious moments. Contact us today and let's create something beautiful together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary">
              <a href="tel:+447886161245">Call Now</a>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
              <Link href="/services">View Our Services</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
