import CallToActionSection from "@/components/pages/home/<USER>";
// import FeaturedServiceSection from "@/components/pages/home/<USER>";
import { HeroSection } from "@/components/pages/home/<USER>";
import IntroSection from "@/components/pages/home/<USER>";
import ServiceSection from "@/components/pages/home/<USER>";
import TestimonialSection from "@/components/pages/home/<USER>";
import { StructuredData } from "@/components/structured-data";

export default function Home() {
   return (
      <div className="min-h-screen">
         <StructuredData
            type="service"
            data={{
               serviceType: "Photography and Videography Services",
               description:
                  "Professional photography and videography services including weddings, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth rental, and dry ice machine rental.",
               price: "From £150",
            }}
         />

         <HeroSection />

         <IntroSection />

         <ServiceSection />

         {/* <FeaturedServiceSection /> */}

         <TestimonialSection />

         <CallToActionSection />
      </div>
   );
}
