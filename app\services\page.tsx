import { ServiceCard } from "@/components/service-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Camera, Video, RotateCcw, <PERSON>f<PERSON> } from "lucide-react";

const services = [
  {
    title: "Wedding Photography",
    description: "Capture your special day with stunning, timeless photographs that tell your unique love story.",
    image: "/images/wedding-shoots/wedding-shoot-1.JPG",
    href: "/services/wedding",
    features: ["Full day coverage", "Engagement session", "Online gallery", "Print release"],
    price: "From £800"
  },
  {
    title: "Pre-Wedding Shoots",
    description: "Beautiful engagement and pre-wedding photography sessions in stunning locations.",
    image: "/images/pre-wedding-shoots/pre-wedding-shoots-1.JPG",
    href: "/services/pre-wedding",
    features: ["Location scouting", "Outfit changes", "Edited photos", "Same day preview"],
    price: "From £300"
  },
  {
    title: "Pregnancy Photography",
    description: "Celebrate this magical time with elegant maternity portraits that capture the beauty of pregnancy.",
    image: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
    href: "/services/pregnancy",
    features: ["Studio or outdoor", "Partner included", "Wardrobe assistance", "Digital gallery"],
    price: "From £250"
  },
  {
    title: "Child Dedication",
    description: "Document these precious ceremonies with sensitivity and artistic vision.",
    image: "/images/child-dedication/child-dedication-1.JPG",
    href: "/services/child-dedication",
    features: ["Ceremony coverage", "Family portraits", "Candid moments", "Digital delivery"],
    price: "From £200"
  },
  {
    title: "Videography Services",
    description: "Professional video production for weddings, events, and special occasions.",
    image: "/images/videography.jpg",
    href: "/services/videography",
    features: ["4K recording", "Drone footage", "Professional editing", "Same day highlights"],
    price: "From £600"
  },
  {
    title: "360 Video Booth",
    description: "Add excitement to your event with our state-of-the-art 360 video booth rental.",
    image: "/images/360-photo-booth.jpg",
    href: "/services/360-booth",
    features: ["Professional setup", "Instant sharing", "Custom branding", "Attendant included"],
    price: "From £400/day"
  },
  {
    title: "Dry Ice Machine Rental",
    description: "Create magical atmospheric effects for your special events and photography sessions.",
    image: "/images/dry-ice-machine.jpg",
    href: "/services/dry-ice",
    features: ["Professional grade", "Safe operation", "Setup included", "Multiple effects"],
    price: "From £150/day"
  }
];

const processSteps = [
  {
    step: "1",
    title: "Consultation",
    description: "We start with a detailed consultation to understand your vision, preferences, and requirements."
  },
  {
    step: "2",
    title: "Planning",
    description: "We create a comprehensive plan including timeline, locations, and shot list tailored to your needs."
  },
  {
    step: "3",
    title: "Capture",
    description: "On the day, we capture every precious moment with professional equipment and artistic expertise."
  },
  {
    step: "4",
    title: "Delivery",
    description: "We carefully edit and deliver your photos/videos in high resolution through our online gallery."
  }
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-background to-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Our <span className="text-primary">Services</span>
          </h1>
          <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
            From intimate moments to grand celebrations, we offer comprehensive photography and videography services 
            tailored to capture your unique story. Each service is delivered with professional excellence and artistic vision.
          </p>
          <Button asChild size="lg">
            <Link href="/contact">Get Your Custom Quote</Link>
          </Button>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <ServiceCard key={index} {...service} />
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Process</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We follow a proven process to ensure every project exceeds your expectations from start to finish.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {step.step}
                  </div>
                  <CardTitle>{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {step.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Contact us today to discuss your photography and videography needs. We'll create a custom package that's perfect for your event.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary">
              <Link href="/contact">Get Quote</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
              <Link href="/portfolio">View Our Work</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
