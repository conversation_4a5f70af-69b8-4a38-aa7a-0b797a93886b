import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { RotateCcw, Users, Smartphone, Settings, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Basic",
    price: "£400",
    duration: "4 hours",
    features: [
      "4 hours of 360 booth rental",
      "Professional setup & breakdown",
      "Trained booth attendant",
      "Instant video sharing",
      "Basic props included",
      "Digital delivery of all videos"
    ]
  },
  {
    name: "Premium",
    price: "£600",
    duration: "6 hours",
    features: [
      "6 hours of 360 booth rental",
      "Professional setup & breakdown",
      "Trained booth attendant",
      "Instant video sharing",
      "Premium props package",
      "Custom branding overlay",
      "Social media integration",
      "Digital delivery + USB"
    ]
  },
  {
    name: "Full Day",
    price: "£800",
    duration: "8 hours",
    features: [
      "8 hours of 360 booth rental",
      "Professional setup & breakdown",
      "Trained booth attendant",
      "Instant video sharing",
      "Deluxe props package",
      "Custom branding & backdrop",
      "Social media integration",
      "Live streaming capability",
      "Digital delivery + premium USB"
    ]
  }
];

const features = [
  {
    icon: RotateCcw,
    title: "360° Video Experience",
    description: "State-of-the-art 360-degree video booth that captures stunning slow-motion videos from every angle."
  },
  {
    icon: Users,
    title: "Group Friendly",
    description: "Large platform accommodates multiple people, making it perfect for group celebrations and parties."
  },
  {
    icon: Smartphone,
    title: "Instant Sharing",
    description: "Videos are processed instantly and can be shared immediately via QR code, email, or social media."
  },
  {
    icon: Settings,
    title: "Professional Setup",
    description: "Our team handles complete setup, operation, and breakdown, ensuring a seamless experience for your event."
  }
];

const eventTypes = [
  {
    title: "Weddings",
    description: "Add excitement to your wedding reception with a 360 video booth that guests will love."
  },
  {
    title: "Corporate Events",
    description: "Perfect for product launches, company parties, and team building events with custom branding."
  },
  {
    title: "Birthday Parties",
    description: "Make any birthday celebration unforgettable with this unique entertainment experience."
  },
  {
    title: "Graduations",
    description: "Celebrate achievements with a fun way for graduates and families to capture memories."
  },
  {
    title: "Anniversaries",
    description: "Create lasting memories for milestone anniversaries with this innovative video experience."
  },
  {
    title: "Holiday Parties",
    description: "Add festive fun to holiday celebrations with themed props and custom branding."
  }
];

export default function Booth360Page() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/360-photo-booth.jpg"
            alt="360 video booth rental hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            360 Video Booth Rental
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Add excitement to your event with our state-of-the-art 360 video booth that creates stunning slow-motion videos
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your 360 Booth</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">See It In Action</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Our 360 Video Booth</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our professional-grade 360 video booth provides an unforgettable experience that your guests will be talking about long after your event.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <feature.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Event Types Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Perfect for Any Event</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our 360 video booth is the perfect addition to any celebration, creating memorable experiences for guests of all ages.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {eventTypes.map((event, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{event.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    {event.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">360 Video Booth Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your event. All packages include professional setup, attendant, and instant video sharing.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} rental</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Book This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Simple steps to create amazing 360-degree videos at your event.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-lg font-semibold mb-2">Step On Platform</h3>
              <p className="text-muted-foreground">Guests step onto the platform and strike a pose</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-lg font-semibold mb-2">Camera Rotates</h3>
              <p className="text-muted-foreground">The camera rotates 360 degrees capturing slow-motion video</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-lg font-semibold mb-2">Instant Processing</h3>
              <p className="text-muted-foreground">Video is processed instantly with effects and branding</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">4</div>
              <h3 className="text-lg font-semibold mb-2">Share & Enjoy</h3>
              <p className="text-muted-foreground">Guests receive their video instantly via QR code or email</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Add Excitement to Your Event?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Book our 360 video booth and give your guests an unforgettable experience they'll be sharing for weeks.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Book Your 360 Video Booth</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
