import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Snowflake, Camera, Shield, Settings, CheckCircle } from "lucide-react";

const packages = [
  {
    name: "Basic",
    price: "£150",
    duration: "4 hours",
    features: [
      "4 hours dry ice machine rental",
      "Professional grade machine",
      "Setup and operation guide",
      "Safety equipment included",
      "Basic effects package",
      "Phone support"
    ]
  },
  {
    name: "Premium",
    price: "£250",
    duration: "6 hours",
    features: [
      "6 hours dry ice machine rental",
      "Professional grade machine",
      "On-site setup and operation",
      "Safety equipment included",
      "Advanced effects package",
      "Trained operator included",
      "Multiple effect options"
    ]
  },
  {
    name: "Full Service",
    price: "£400",
    duration: "8 hours",
    features: [
      "8 hours dry ice machine rental",
      "Professional grade machine",
      "Full operator service",
      "Safety equipment included",
      "Complete effects package",
      "Trained operator included",
      "Custom timing coordination",
      "Photography integration"
    ]
  }
];

const features = [
  {
    icon: Snowflake,
    title: "Professional Equipment",
    description: "High-quality dry ice machines that produce consistent, safe, and dramatic fog effects for any event."
  },
  {
    icon: Camera,
    title: "Photography Perfect",
    description: "Creates stunning atmospheric effects that enhance photography and videography with magical ambiance."
  },
  {
    icon: Shield,
    title: "Safe Operation",
    description: "All equipment includes safety features and we provide comprehensive safety guidelines and equipment."
  },
  {
    icon: Settings,
    title: "Customizable Effects",
    description: "Various effect options from subtle ambiance to dramatic reveals, perfectly timed for your event."
  }
];

const applications = [
  {
    title: "Wedding Photography",
    description: "Create romantic, dreamy atmospheres for wedding photos with low-lying fog effects that add magic to your shots."
  },
  {
    title: "First Dance",
    description: "Transform your first dance into a fairytale moment with beautiful fog effects that create an intimate atmosphere."
  },
  {
    title: "Event Entrances",
    description: "Make dramatic entrances at corporate events, parties, or celebrations with impressive fog effects."
  },
  {
    title: "Photography Sessions",
    description: "Enhance portrait and fashion photography sessions with atmospheric effects that add depth and drama."
  },
  {
    title: "Stage Performances",
    description: "Perfect for theatrical performances, concerts, and presentations requiring atmospheric enhancement."
  },
  {
    title: "Product Launches",
    description: "Create memorable product reveals and launches with dramatic fog effects that captivate audiences."
  }
];

export default function DryIcePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/dry-ice-machine.jpg"
            alt="Dry ice machine rental hero"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Dry Ice Machine Rental
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed">
            Create magical atmospheric effects for your events and photography sessions with our professional dry ice machines
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Rent Dry Ice Machine</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-black">
              <Link href="/portfolio">See Effects Gallery</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Our Dry Ice Machines</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our professional-grade dry ice machines provide safe, reliable, and stunning atmospheric effects for any occasion.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <feature.icon className="h-12 w-12 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Applications Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Perfect Applications</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Dry ice effects can transform any event or photography session, creating memorable moments and stunning visuals.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {applications.map((application, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{application.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    {application.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Dry Ice Machine Rental Packages</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the perfect package for your event. All packages include professional equipment and safety guidelines.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <CardDescription>{pkg.duration} rental</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button asChild className="w-full mt-6">
                    <Link href="/contact">Rent This Package</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Safety Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">Safety First</h2>
            <div className="prose prose-lg mx-auto text-muted-foreground">
              <p className="mb-6">
                Safety is our top priority when it comes to dry ice effects. All our equipment meets professional safety standards, 
                and we provide comprehensive safety guidelines and equipment with every rental.
              </p>
              <p className="mb-6">
                Our machines are designed for safe operation with built-in safety features, and we offer training and support 
                to ensure proper handling. For premium packages, our trained operators handle all aspects of the equipment 
                operation, allowing you to focus on your event.
              </p>
              <p>
                We provide all necessary safety equipment including gloves, tongs, and ventilation guidelines to ensure 
                a safe and successful event experience.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Create Magical Effects?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto opacity-90">
            Transform your event or photography session with stunning dry ice effects that will leave a lasting impression.
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/contact">Rent Your Dry Ice Machine</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
