import {
   <PERSON>,
   CardContent,
   CardDescription,
   <PERSON><PERSON><PERSON><PERSON>,
   CardTitle,
} from "@/components/ui/card";
import { testimonials } from "@/lib/data";
import { Quote, <PERSON> } from "lucide-react";
import Image from "next/image";

export default function TestimonialSection() {
   return (
      <>
         <section className="py-20 bg-astral-grey">
            <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     What Our Clients Say
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Don&apos;t just take our word for it. Here&apos;s what our
                     happy clients have to say about their experience with
                     Astral Studios.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {testimonials.map((testimonial, index) => (
                     <Card key={index} className="relative">
                        <CardHeader>
                           <div className="flex items-center space-x-4">
                              <div className="relative w-12 h-12 rounded-full overflow-hidden">
                                 <Image
                                    src={testimonial.image}
                                    alt={testimonial.name}
                                    fill
                                    className="object-cover"
                                    sizes="48px"
                                 />
                              </div>
                              <div>
                                 <CardTitle className="text-lg">
                                    {testimonial.name}
                                 </CardTitle>
                                 <CardDescription>
                                    {testimonial.service}
                                 </CardDescription>
                              </div>
                           </div>
                           <div className="flex space-x-1">
                              {[...Array(testimonial.rating)].map((_, i) => (
                                 <Star
                                    key={i}
                                    className="h-4 w-4 fill-primary text-primary"
                                 />
                              ))}
                           </div>
                        </CardHeader>
                        <CardContent>
                           <Quote className="h-6 w-6 text-muted-foreground mb-2" />
                           <p className="text-muted-foreground italic">
                              &quot;{testimonial.text}&quot;
                           </p>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>
      </>
   );
}
